# Example AWS Image Builder Infrastructure Configuration with SCCM Agent Support
# This configuration includes environment variables for SCCM agent installation

name: WindowsServer2022InfrastructureWithSCCM
description: Infrastructure configuration for Windows Server 2022 builds with SCCM agent support
instanceTypes:
  - m5.large
  - m5.xlarge
  - m5.2xlarge

# Instance profile with necessary permissions
instanceProfileName: EC2InstanceProfileForImageBuilderWithSCCM

# Security group allowing SCCM communication
securityGroupIds:
  - sg-0123456789abcdef0  # Replace with your security group ID

# Subnet for build instances
subnetId: subnet-0123456789abcdef0  # Replace with your subnet ID

# Key pair for troubleshooting (optional)
keyPair: your-key-pair-name  # Replace with your key pair name

# Termination policy
instanceMetadataOptions:
  httpTokens: required
  httpPutResponseHopLimit: 2

# Environment variables for SCCM agent installation
environmentVariables:
  # Required SCCM parameters
  - name: SCCM_SITE_CODE
    value: "{{ssm:/imagebuilder/sccm/site-code}}"
  
  - name: SCCM_MANAGEMENT_POINT
    value: "{{ssm:/imagebuilder/sccm/management-point}}"
  
  # Optional SCCM parameters
  - name: SCCM_INSTALLER_URL
    value: "{{ssm:/imagebuilder/sccm/installer-url}}"
  
  - name: SCCM_INSTALL_PROPERTIES
    value: "{{ssm:/imagebuilder/sccm/install-properties}}"
  
  # Additional environment variables for other components
  - name: COMPANY_NAME
    value: "{{ssm:/imagebuilder/company/name}}"
  
  - name: ENVIRONMENT_TYPE
    value: "{{ssm:/imagebuilder/environment/type}}"

# Logging configuration
logging:
  s3Logs:
    s3BucketName: your-imagebuilder-logs-bucket  # Replace with your S3 bucket
    s3KeyPrefix: infrastructure-logs/

# SNS topic for notifications (optional)
snsTopicArn: arn:aws:sns:us-east-1:123456789012:imagebuilder-notifications  # Replace with your SNS topic ARN

# Resource tags
resourceTags:
  Environment: Production
  Project: ImageBuilder
  Component: SCCM-Enabled-Infrastructure
  ManagedBy: ImageBuilder
  CostCenter: IT-Infrastructure

# Terminate instance on failure
terminateInstanceOnFailure: true

# Additional user data script (optional)
# This can be used for additional pre-build configuration
userData: |
  <powershell>
  # Additional PowerShell commands to run before image building
  Write-Host "Starting ImageBuilder instance with SCCM support..."
  
  # Set timezone (example)
  Set-TimeZone -Id "Eastern Standard Time"
  
  # Configure Windows Event Logs (example)
  wevtutil sl Application /ms:67108864
  wevtutil sl System /ms:67108864
  wevtutil sl Security /ms:67108864
  
  # Create additional temp directories if needed
  New-Item -ItemType Directory -Path "C:\ImageBuilder" -Force
  New-Item -ItemType Directory -Path "C:\Logs" -Force
  
  Write-Host "Pre-build configuration completed."
  </powershell>
