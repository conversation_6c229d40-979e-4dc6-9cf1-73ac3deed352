# Deploy SCCM Agent Component to AWS Image Builder
# This script deploys the SCCM agent installation component to AWS Image Builder

[CmdletBinding()]
param(
    [Parameter(Mandatory=$false)]
    [string]$Region = "us-east-1",
    
    [Parameter(Mandatory=$false)]
    [string]$ComponentName = "win-server-sccm-agent",
    
    [Parameter(Mandatory=$false)]
    [string]$ComponentVersion = "1.0.0",
    
    [Parameter(Mandatory=$true)]
    [string]$SiteCode,
    
    [Parameter(Mandatory=$true)]
    [string]$ManagementPoint,
    
    [Parameter(Mandatory=$false)]
    [string]$InstallerUrl,
    
    [Parameter(Mandatory=$false)]
    [string]$InstallProperties,
    
    [Parameter(Mandatory=$false)]
    [switch]$CreateParameters,
    
    [Parameter(Mandatory=$false)]
    [switch]$UpdateParameters,
    
    [Parameter(Mandatory=$false)]
    [switch]$Force
)

# Set AWS region
$env:AWS_DEFAULT_REGION = $Region

Write-Host "Deploying SCCM Agent Component to AWS Image Builder" -ForegroundColor Green
Write-Host "Region: $Region" -ForegroundColor Cyan
Write-Host "Component: $ComponentName v$ComponentVersion" -ForegroundColor Cyan

# Function to check AWS CLI availability
function Test-AWSCLIAvailable {
    try {
        $null = aws --version 2>$null
        return $true
    } catch {
        return $false
    }
}

# Function to create or update SSM parameters
function Set-SSMParameters {
    param(
        [string]$SiteCode,
        [string]$ManagementPoint,
        [string]$InstallerUrl,
        [string]$InstallProperties
    )
    
    Write-Host "Creating/updating Systems Manager parameters..." -ForegroundColor Yellow
    
    # Required parameters
    $parameters = @(
        @{
            Name = "/imagebuilder/sccm/site-code"
            Value = $SiteCode
            Description = "SCCM Site Code for ImageBuilder"
        },
        @{
            Name = "/imagebuilder/sccm/management-point"
            Value = $ManagementPoint
            Description = "SCCM Management Point FQDN for ImageBuilder"
        }
    )
    
    # Optional parameters
    if ($InstallerUrl) {
        $parameters += @{
            Name = "/imagebuilder/sccm/installer-url"
            Value = $InstallerUrl
            Description = "SCCM Installer Download URL for ImageBuilder"
        }
    }
    
    if ($InstallProperties) {
        $parameters += @{
            Name = "/imagebuilder/sccm/install-properties"
            Value = $InstallProperties
            Description = "Additional SCCM Installation Properties for ImageBuilder"
        }
    }
    
    foreach ($param in $parameters) {
        try {
            Write-Host "Setting parameter: $($param.Name)" -ForegroundColor Cyan
            
            $result = aws ssm put-parameter `
                --name $param.Name `
                --value $param.Value `
                --type "String" `
                --description $param.Description `
                --overwrite 2>&1
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "Parameter $($param.Name) set successfully" -ForegroundColor Green
            } else {
                Write-Warning "Failed to set parameter $($param.Name): $result"
            }
        } catch {
            Write-Error "Error setting parameter $($param.Name): $($_.Exception.Message)"
        }
    }
}

# Function to deploy the component
function Deploy-SCCMComponent {
    param(
        [string]$ComponentPath,
        [string]$ComponentName,
        [bool]$ForceUpdate
    )
    
    Write-Host "Deploying Image Builder component..." -ForegroundColor Yellow
    
    if (!(Test-Path $ComponentPath)) {
        Write-Error "Component file not found: $ComponentPath"
        return $false
    }
    
    try {
        # Check if component already exists
        $existingComponent = aws imagebuilder list-components `
            --filters "name=name,values=$ComponentName" `
            --query "componentVersionList[0].arn" `
            --output text 2>$null
        
        if ($existingComponent -and $existingComponent -ne "None" -and !$ForceUpdate) {
            Write-Host "Component $ComponentName already exists: $existingComponent" -ForegroundColor Yellow
            Write-Host "Use -Force to update the component" -ForegroundColor Yellow
            return $true
        }
        
        Write-Host "Creating component from: $ComponentPath" -ForegroundColor Cyan
        
        $result = aws imagebuilder create-component `
            --cli-input-yaml "file://$ComponentPath" 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            $componentArn = ($result | ConvertFrom-Json).componentBuildVersionArn
            Write-Host "Component created successfully" -ForegroundColor Green
            Write-Host "Component ARN: $componentArn" -ForegroundColor Cyan
            return $true
        } else {
            if ($result -like "*already exists*") {
                Write-Host "Component already exists" -ForegroundColor Yellow
                return $true
            } else {
                Write-Error "Failed to create component: $result"
                return $false
            }
        }
    } catch {
        Write-Error "Error deploying component: $($_.Exception.Message)"
        return $false
    }
}

# Function to validate component
function Test-ComponentDeployment {
    param([string]$ComponentName)
    
    Write-Host "Validating component deployment..." -ForegroundColor Yellow
    
    try {
        $component = aws imagebuilder list-components `
            --filters "name=name,values=$ComponentName" `
            --query "componentVersionList[0]" `
            --output json 2>$null
        
        if ($component -and $component -ne "null") {
            $componentData = $component | ConvertFrom-Json
            Write-Host "Component validation successful" -ForegroundColor Green
            Write-Host "Name: $($componentData.name)" -ForegroundColor Cyan
            Write-Host "Version: $($componentData.version)" -ForegroundColor Cyan
            Write-Host "ARN: $($componentData.arn)" -ForegroundColor Cyan
            return $true
        } else {
            Write-Error "Component validation failed: Component not found"
            return $false
        }
    } catch {
        Write-Error "Error validating component: $($_.Exception.Message)"
        return $false
    }
}

# Main execution
try {
    # Validate prerequisites
    if (!(Test-AWSCLIAvailable)) {
        Write-Error "AWS CLI is not available. Please install and configure AWS CLI."
        exit 1
    }
    
    # Test AWS credentials
    $identity = aws sts get-caller-identity 2>$null
    if ($LASTEXITCODE -ne 0) {
        Write-Error "AWS credentials not configured or invalid. Please run 'aws configure'."
        exit 1
    }
    
    $identityData = $identity | ConvertFrom-Json
    Write-Host "AWS credentials validated for account: $($identityData.Account)" -ForegroundColor Green
    
    # Create or update SSM parameters if requested
    if ($CreateParameters -or $UpdateParameters) {
        Set-SSMParameters -SiteCode $SiteCode -ManagementPoint $ManagementPoint -InstallerUrl $InstallerUrl -InstallProperties $InstallProperties
    }
    
    # Deploy the component
    $componentPath = Join-Path $PSScriptRoot "install-sccm-agent.yml"
    if (!(Deploy-SCCMComponent -ComponentPath $componentPath -ComponentName $ComponentName -ForceUpdate $Force)) {
        exit 1
    }
    
    # Validate deployment
    if (!(Test-ComponentDeployment -ComponentName $ComponentName)) {
        exit 1
    }
    
    Write-Host "`nSCCM Agent component deployment completed successfully!" -ForegroundColor Green
    Write-Host "`nNext steps:" -ForegroundColor Yellow
    Write-Host "1. Add the component to your ImageBuilder recipe" -ForegroundColor White
    Write-Host "2. Update your infrastructure configuration with environment variables" -ForegroundColor White
    Write-Host "3. Test the component in a build pipeline" -ForegroundColor White
    
    Write-Host "`nExample recipe component entry:" -ForegroundColor Yellow
    Write-Host "components:" -ForegroundColor White
    Write-Host "  - name: $ComponentName" -ForegroundColor White
    Write-Host "    parameters: []" -ForegroundColor White
    
} catch {
    Write-Error "Deployment failed: $($_.Exception.Message)"
    exit 1
}
